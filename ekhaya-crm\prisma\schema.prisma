// This schema.prisma file connects to the same Neon database as your main website
// It only includes admin-relevant models and views

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Admin User Management (separate from customer users)
model AdminUser {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      AdminRole @default(ADMIN)
  isActive  Boolean   @default(true)
  lastLogin DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  STAFF
  VIEWER
}

// These models reference the main database tables
// User model from main database
model User {
  id                String       @id @default(cuid())
  name              String?
  email             String       @unique
  emailVerified     DateTime?
  image             String?
  phone             String?
  address           String?
  city              String?
  province          String?
  postalCode        String?
  dateOfBirth       DateTime?
  gender            String?
  loyaltyPoints     Int          @default(0)
  isActive          Boolean      @default(true)
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  // Relations
  accounts          Account[]
  sessions          Session[]
  vehicles          Vehicle[]
  bookings          Booking[]
  reviews           Review[]
  notifications     Notification[]
  paymentMethods    PaymentMethod[]
  membership        Membership?
  stripeCustomer    StripeCustomer?
}

model Booking {
  id           String        @id @default(cuid())
  userId       String
  serviceId    String
  vehicleId    String?
  bookingDate  DateTime
  timeSlot     String
  status       BookingStatus @default(PENDING)
  totalAmount  Int
  notes        String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  service Service  @relation(fields: [serviceId], references: [id])
  vehicle Vehicle? @relation(fields: [vehicleId], references: [id])
  payment Payment?
  addons  BookingAddon[]
  review  Review?

  @@index([status])
  @@index([bookingDate])
  @@index([userId])
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

model Service {
  id          String    @id @default(cuid())
  name        String
  description String
  price       Int       // Price in cents
  duration    Int       // Duration in minutes
  category    String
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  bookings Booking[]
  addons   Addon[]
}

model Payment {
  id              String         @id @default(cuid())
  bookingId       String?        @unique
  paymentMethodId String?
  amount          Int            // Amount in cents
  status          PaymentStatus  @default(PENDING)
  transactionId   String?        // Generic transaction ID
  paymentDate     DateTime?

  // Payment method type for display
  paymentMethodType String?      // 'card', 'cash', 'eft', 'payfast', etc.

  // Stripe-specific fields
  stripePaymentIntentId String?  // Stripe Payment Intent ID
  stripeChargeId        String?  // Stripe Charge ID
  stripeCustomerId      String?  // Stripe Customer ID
  stripeFee             Int?     // Stripe processing fee in cents
  stripeReceiptUrl      String?  // Stripe receipt URL

  // Additional payment metadata
  currency        String         @default("ZAR") // South African Rand
  description     String?        // Payment description
  failureReason   String?        // Reason for payment failure
  refundReason    String?        // Reason for refund
  refundedAmount  Int?           // Amount refunded in cents

  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  booking       Booking?       @relation(fields: [bookingId], references: [id])
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])

  @@index([status])
  @@index([paymentDate])
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

model Membership {
  id         String   @id @default(cuid())
  userId     String   @unique
  plan       String   // BASIC, PREMIUM, etc.
  price      Int      // Price in cents
  startDate  DateTime
  endDate    DateTime?
  isActive   Boolean  @default(false)
  autoRenew  Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([isActive])
  @@index([endDate])
}

// Other required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Vehicle {
  id           String      @id @default(cuid())
  userId       String
  make         String
  model        String
  year         Int
  color        String
  licensePlate String
  vehicleType  VehicleType
  isPrimary    Boolean     @default(false)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  bookings Booking[]
}

enum VehicleType {
  SEDAN
  SUV
  HATCHBACK
  COUPE
  CONVERTIBLE
  PICKUP
  VAN
  MOTORCYCLE
  OTHER
}

model Review {
  id        String   @id @default(cuid())
  bookingId String   @unique
  userId    String
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())

  booking Booking @relation(fields: [bookingId], references: [id])
  user    User    @relation(fields: [userId], references: [id])
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType @default(GENERAL)
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  user User @relation(fields: [userId], references: [id])

  @@index([isRead])
  @@index([createdAt])
}

enum NotificationType {
  BOOKING
  PAYMENT
  PROMOTION
  SYSTEM
  GENERAL
}

model PaymentMethod {
  id                String            @id @default(cuid())
  userId            String
  type              PaymentMethodType
  lastFour          String?           // Optional for non-card payments
  expiryMonth       Int?              // Optional for non-card payments
  expiryYear        Int?              // Optional for non-card payments
  cardholderName    String?           // Optional for non-card payments
  isDefault         Boolean           @default(false)
  isActive          Boolean           @default(true)

  // Stripe-specific fields
  stripePaymentMethodId String?       // Stripe Payment Method ID
  stripeBrand           String?       // Card brand from Stripe (visa, mastercard, etc.)
  stripeFingerprint     String?       // Stripe card fingerprint for duplicate detection

  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@index([userId])
  @@index([isDefault])
}

enum PaymentMethodType {
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  CASH
  MOBILE_PAYMENT
}

model StripeCustomer {
  userId            String   @id
  stripeCustomerId  String   @unique  // Stripe Customer ID
  email             String?  // Email used in Stripe
  name              String?  // Name used in Stripe
  phone             String?  // Phone used in Stripe
  defaultPaymentMethodId String? // Default Stripe Payment Method ID
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([stripeCustomerId])
  @@index([email])
}

model Addon {
  id          String    @id @default(cuid())
  name        String
  description String
  price       Int       // Price in cents
  category    String
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  services Service[]
  bookingAddons BookingAddon[]
}

model BookingAddon {
  id        String   @id @default(cuid())
  bookingId String
  addonId   String
  quantity  Int      @default(1)
  price     Int      // Price at time of booking
  createdAt DateTime @default(now())

  booking Booking @relation(fields: [bookingId], references: [id])
  addon   Addon   @relation(fields: [addonId], references: [id])

  @@unique([bookingId, addonId])
}